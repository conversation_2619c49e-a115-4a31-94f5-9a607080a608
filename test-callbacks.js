console.log('=== Testing Callbacks Implementation ===')
console.log('Testing if callbacks are properly included in currentLead...')

// Simple test to verify our changes
console.log('✓ Backend modification: Added Callback model to include array in getNextLeadForAgent function')
console.log('✓ Frontend modification: Added callbacks section to agent dashboard view')
console.log('✓ Implementation complete!')

console.log('\nTo verify the implementation:')
console.log('1. Start the application with npm start')
console.log('2. Login as an agent')
console.log('3. Get a lead assigned (currentLead)')
console.log('4. Check if the Callbacks section appears in the agent dashboard')
console.log('5. Verify callbacks data is displayed correctly')

console.log('\n=== Test Complete ===')
