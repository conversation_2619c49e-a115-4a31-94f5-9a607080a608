'use strict'

angular.module('dialerFrontendApp')
    .directive('hierarchicalAssignmentTree', function () {
        return {
            restrict: 'E',
            scope: {
                data: '=',
                selectedItems: '=',
                expandedNodes: '=',
                onToggleSelection: '&',
                onToggleExpanded: '&',
                isSelected: '&',
                isExpanded: '&',
                getAssignmentStatus: '&',
                getAssignmentStatusClass: '&',
                depth: '=?'
            },
            templateUrl: 'views/directives/hierarchical-assignment-tree.html',
            link: function (scope) {
                console.log('🔍 hierarchical-assignment-tree: Directive linking, depth:', scope.depth, 'data length:', scope.data ? scope.data.length : 'no data')

                // Initialize depth tracking to prevent infinite recursion
                scope.depth = scope.depth || 0
                scope.maxDepth = 5 // Limit recursion depth

                if (scope.depth > scope.maxDepth) {
                    console.warn('🔍 hierarchical-assignment-tree: Max depth exceeded, stopping recursion')
                    return
                }

                // Pre-compute node icons and colors to avoid function calls in template
                scope.nodeIcons = {
                    'stage': 'fa-folder',
                    'agent': 'fa-user',
                    'leadType': 'fa-tag',
                    'default': 'fa-circle'
                }

                scope.nodeColors = {
                    'stage': 'text-primary',
                    'agent': 'text-info',
                    'leadType': 'text-warning',
                    'default': 'text-muted'
                }

                // Helper function to get node icon based on type
                scope.getNodeIcon = function (node) {
                    return scope.nodeIcons[node.type] || scope.nodeIcons.default
                }

                // Helper function to get node color based on type
                scope.getNodeColor = function (node) {
                    return scope.nodeColors[node.type] || scope.nodeColors.default
                }
                
                // Check if node has children and we haven't exceeded max depth
                scope.hasChildren = function (node) {
                    if (scope.depth >= scope.maxDepth) {
                        return false
                    }
                    return (node.agents && node.agents.length > 0) ||
                           (node.leadTypes && node.leadTypes.length > 0) ||
                           (node.stages && node.stages.length > 0)
                }
                
                // Check if node is selectable (agents are selectable for assignment)
                scope.isSelectable = function (node) {
                    return node.type === 'agent'
                }
                
                // Get children of a node
                scope.getChildren = function (node) {
                    if (node.agents) return node.agents
                    if (node.leadTypes) return node.leadTypes
                    if (node.stages) return node.stages
                    return []
                }
                
                // Toggle node expansion
                scope.toggleExpanded = function (node) {
                    console.log('🔍 toggleExpanded called for:', node.name)
                    scope.onToggleExpanded({ node: node })
                }

                // Toggle node selection
                scope.toggleSelection = function (node) {
                    console.log('🔍 toggleSelection called for:', node.name)
                    scope.onToggleSelection({ item: node })
                }

                // Check if node is expanded
                scope.isNodeExpanded = function (node) {
                    var nodeId = node.id + '_' + node.type
                    var expanded = scope.isExpanded({ nodeId: nodeId })
                    return expanded
                }

                // Check if node is selected
                scope.isNodeSelected = function (node) {
                    return scope.isSelected({ item: node })
                }

                // Get node ID for expansion tracking
                scope.getNodeId = function (node) {
                    return node.id + '_' + node.type
                }
                
                // Get assignment status
                scope.getNodeAssignmentStatus = function (node) {
                    return scope.getAssignmentStatus({ item: node })
                }
                
                // Get assignment status class
                scope.getNodeAssignmentStatusClass = function (node) {
                    return scope.getAssignmentStatusClass({ item: node })
                }
            }
        }
    })
