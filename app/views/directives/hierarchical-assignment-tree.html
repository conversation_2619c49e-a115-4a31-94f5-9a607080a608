<!-- SAFE: Non-recursive flat display -->
<div class="hierarchical-tree">
    <div class="alert alert-info" style="font-size: 12px; padding: 8px;">
        <strong>Agent Assignment Data:</strong>
        {{ data ? data.length : 'no data' }} items loaded
    </div>

    <!-- Display all items in a flat structure with interactive features -->
    <div ng-if="data && data.length > 0">
        <div ng-repeat="node in data track by (node.id + '_' + node.type)" class="tree-node">
            <div class="tree-node-content">
                <!-- Expand/Collapse button -->
                <button
                    ng-if="hasChildren(node)"
                    class="btn btn-xs btn-link tree-toggle"
                    ng-click="toggleExpanded(node)"
                    type="button">
                    <i class="fa" ng-class="isNodeExpanded(node) ? 'fa-minus-square-o' : 'fa-plus-square-o'"></i>
                </button>
                <span ng-if="!hasChildren(node)" class="tree-spacer"></span>

                <!-- No checkboxes in view-only mode -->
                <span class="tree-spacer"></span>

                <!-- Node icon -->
                <i class="fa tree-icon" ng-class="getNodeIcon(node)"></i>

                <!-- Node label -->
                <span class="tree-label" ng-class="getNodeColor(node)">
                    {{ node.name }} ({{ node.type }})
                </span>

                <!-- Show children count -->
                <span ng-if="hasChildren(node)" class="text-muted" style="margin-left: 10px;">
                    [{{ getChildren(node).length }} children]
                </span>

                <!-- Assignment status with color coding -->
                <span ng-if="node.assigned !== undefined"
                      class="tree-status"
                      ng-class="getNodeAssignmentStatusClass(node)">
                    - {{ getNodeAssignmentStatus(node) }}
                </span>
            </div>

            <!-- Show children in a simple list (only when expanded) -->
            <div ng-if="hasChildren(node) && isNodeExpanded(node)" class="tree-children" style="margin-left: 20px; margin-top: 5px;">
                <div ng-repeat="child in getChildren(node)" class="child-item" style="padding: 4px 0;">
                    <i class="fa fa-arrow-right text-muted" style="margin-right: 8px;"></i>
                    <i class="fa" ng-class="getNodeIcon(child)" style="margin-right: 8px;"></i>
                    <span ng-class="getNodeColor(child)">{{ child.name }} ({{ child.type }})</span>

                    <!-- Assignment status for children -->
                    <span ng-if="child.assigned !== undefined"
                          ng-class="getNodeAssignmentStatusClass(child)"
                          style="margin-left: 8px;">
                        - {{ getNodeAssignmentStatus(child) }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div ng-if="!data || data.length === 0" class="alert alert-warning">
        No data available for this view.
    </div>
</div>

<style>
.hierarchical-tree {
    font-family: inherit;
    padding: 10px;
}

.tree-node {
    margin: 8px 0;
    padding: 8px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.tree-node:hover {
    background-color: #e9ecef;
}

.tree-node-content {
    display: flex;
    align-items: center;
    padding: 4px 0;
}

.tree-toggle {
    padding: 0;
    margin-right: 8px;
    border: none;
    background: none;
    color: #6c757d;
    width: 20px;
    text-align: center;
    cursor: pointer;
}

.tree-toggle:hover {
    color: #495057;
}

.tree-spacer {
    width: 28px;
    display: inline-block;
}

.tree-checkbox {
    margin-right: 8px;
    margin-top: 0;
    margin-bottom: 0;
    cursor: pointer;
}

.tree-icon {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.tree-label {
    font-weight: 500;
    flex-grow: 1;
}

.tree-status {
    font-size: 0.875em;
    font-style: italic;
    margin-left: 8px;
    font-weight: 600;
}

.tree-children {
    margin-left: 16px;
    margin-top: 8px;
    border-left: 2px solid #dee2e6;
    padding-left: 12px;
    background-color: #ffffff;
    border-radius: 0 0 4px 4px;
}

.child-item {
    font-size: 0.9em;
    display: flex;
    align-items: center;
    padding: 2px 0;
    transition: background-color 0.2s ease;
}

.child-item:hover {
    background-color: #f8f9fa;
    border-radius: 3px;
    padding-left: 4px;
    margin-left: -4px;
}

/* Color classes */
.text-primary { color: #007bff !important; }
.text-info { color: #17a2b8 !important; }
.text-warning { color: #ffc107 !important; }
.text-success { color: #28a745 !important; }
.text-muted { color: #6c757d !important; }
.text-danger { color: #dc3545 !important; }
</style>
