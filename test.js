var APP_SETTINGS = require('./config/constants')
var Sequelize = require('sequelize'),
    db = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('./database/schema')(db, Sequelize)

const TEST_STAGE_ID = 1
const TEST_AGENT_ID_1 = 1
const TEST_AGENT_ID_2 = 2

// Test concurrent lead assignment to verify locking mechanism
console.log('Testing concurrent lead assignment...')
testConcurrentLeadAssignment()

// Run the concurrent test
testConcurrentLeadAssignment()

function testConcurrentLeadAssignment() {
    console.log('=== Lead Locking Mechanism Test ===')
    console.log('Testing concurrent lead assignment to verify the fix...')
    console.log('Starting concurrent lead assignment test...')

    // Simulate two agents trying to get leads simultaneously
    const agent1Promise = getNextLeadForAgent(TEST_AGENT_ID_1, 'Agent1')
    const agent2Promise = getNextLeadForAgent(TEST_AGENT_ID_2, 'Agent2')

    Promise.all([agent1Promise, agent2Promise])
        .then(results => {
            const [result1, result2] = results

            console.log('\n=== TEST RESULTS ===')
            console.log('Agent 1 result:', result1 ? `Lead ${result1.leadId}` : 'No lead assigned')
            console.log('Agent 2 result:', result2 ? `Lead ${result2.leadId}` : 'No lead assigned')

            if (result1 && result2 && result1.leadId === result2.leadId) {
                console.log('❌ FAILURE: Both agents got the same lead! Lead ID:', result1.leadId)
                console.log('The locking mechanism is NOT working properly.')
                process.exit(1)
            } else if (result1 || result2) {
                console.log('✅ SUCCESS: No duplicate lead assignment detected')
                console.log('The locking mechanism is working correctly.')

                if (result1 && result2) {
                    console.log(`Both agents got different leads: ${result1.leadId} vs ${result2.leadId}`)
                } else {
                    console.log('Only one agent got a lead, which is expected behavior.')
                }
                process.exit(0)
            } else {
                console.log('⚠️  WARNING: No leads were assigned to either agent')
                console.log('This could mean no leads are available or there\'s a configuration issue.')
                process.exit(0)
            }
        })
        .catch(err => {
            console.error('Test failed with error:', err.message)
            process.exit(1)
        })
}

function getNextLeadForAgent (agentId, agentName) {
    agentId = agentId || TEST_AGENT_ID_1
    agentName = agentName || 'TestAgent'

    console.log(`[${agentName}] Starting lead selection for agent ${agentId}...`)
    var now = new Date()

    var campaignStage = {
        id: TEST_STAGE_ID
    }
   
    var session = {
        agentId: agentId
    }
    
    var callbackLeadIds = []

    for (var i = 1; i < 200; i++) {
        callbackLeadIds.push(i)
    }
    
    var callAttemptWhereQuery = {
        dontContactUntil: {
            $or: {
                $eq: null,
                $lt: now
            }
        },
        $or: [{
            phone_home: {
                $and: {
                    $not: null,
                    $ne: ''
                }
            }
        }, {
            phone_mobile: {
                $and: {
                    $not: null,
                    $ne: ''
                }
            },
        }, {
            phone_work: {
                $and: {
                    $not: null,
                    $ne: ''
                }
            },
        }, {
            phone_workmobile: {
                $and: {
                    $not: null,
                    $ne: ''
                }
            }
        }]
    }

    if (callbackLeadIds && callbackLeadIds.length) {
        callAttemptWhereQuery['id'] = {
            $notIn: callbackLeadIds
        }
    }

    callAttemptWhereQuery.agentPortfolioTag = {
        $or: {
            $in: [''],
            $eq: null
        }
    }

    var result

    return Promise.resolve(
        db.transaction(function (t) {
            return Promise.resolve(
                Models.CallAttempt.findOne({
                    include: [{
                        model: Models.Lead,
                        where: callAttemptWhereQuery,
                        include: [{
                            model: Models.Skill,
                            as: 'tfSkill'
                        }, {
                            model: Models.SubSkill,
                            as: 'tfSubSkill'
                        }, {
                            model: Models.Skill,
                            as: 'tmSkill'
                        }, {
                            model: Models.SubSkill,
                            as: 'tmSubSkill'
                        }, {
                            model: Models.Invoice
                        }, {
                            model: Models.CampaignLead,
                            where: {
                                currentCampaignStageId: campaignStage.id
                            }
                        }, {
                            model: Models.PaymentLog,
                            include: [Models.RecurringPayment, {
                                model: Models.Campaign,
                                attributes: ['id', 'name']
                            }]
                        }]
                    }],
                    where: Sequelize.and({
                        campaignstageId: campaignStage.id
                    }, {
                        startTime: {
                            $lt: '23:59:59'
                        }
                    }, {
                        endTime: {
                            $gt: '00:00:00'
                        }
                    }, {
                        startDate: {
                            $or: {
                                $lt: now,
                                $eq: null
                            }
                        }
                    }, {
                        endDate: {
                            $or: {
                                $gt: now,
                                $eq: null
                            }
                        }
                    }),
                    order: [
                        ['lastDispositionDate'],
                        ['randomSelector']
                    ]
                }, {
                    transaction: t,
                    lock: t.LOCK.UPDATE
                }).then(function (callAttempt) {
                    if (callAttempt) {
                        console.log(`[${agentName}] Selected call attempt ${callAttempt.id} for lead ${callAttempt.leadId}`)
                        return callAttempt.lead.updateAttributes({
                            dontContactUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
                            lastAgent: session.agentId
                        }, {
                            transaction: t
                        }).then(() => {
                            console.log(`[${agentName}] Successfully assigned lead ${callAttempt.leadId} to agent ${session.agentId}`)
                            return callAttempt
                        }).catch(Promise.reject)
                    } else {
                        console.log(`[${agentName}] No available call attempts found`)
                        return Promise.resolve()
                    }
                }).catch(Promise.reject)
            )
        }).then(function (_result) {
            result = _result

            if (result && result.lead) {
                console.log(`[${agentName}] Final result: Lead ${result.leadId} assigned to agent ${result.lead.lastAgent}`)
                return result.lead.reload().then(l => {
                    console.log(`[${agentName}] Lead reload completed, lastAgent: ${l.lastAgent}`)
                    return result
                })
            } else {
                console.log(`[${agentName}] No lead assigned`)
                return result
            }
        }).catch(function (e) {
            console.error(`[${agentName}] Error in lead assignment:`, e.message)
            return Promise.reject(e)
        })
    )
}